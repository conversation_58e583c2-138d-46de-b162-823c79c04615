{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\dashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\dashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\fy0j1ihp..sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\messagescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\messagescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\fy0j1ihp..sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\fy0j1ihp..sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|ResultFrame"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "DashboardController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\DashboardController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\DashboardController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\DashboardController.cs", "RelativeToolTip": "webApi\\Controllers\\DashboardController.cs", "ViewState": "AgIAAAwDAAAAAAAAAAAowBwDAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T01:23:03.412Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SQLQuery1.sql ", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\fy0j1ihp..sql", "ToolTip": "SQLQuery1.sql *", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-29T16:52:04.561Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MessagesController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\MessagesController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\MessagesController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\MessagesController.cs", "RelativeToolTip": "webApi\\Controllers\\MessagesController.cs", "ViewState": "AgIAAGACAAAAAAAAAAAewHMCAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T15:40:32.56Z", "EditorCaption": ""}]}]}]}